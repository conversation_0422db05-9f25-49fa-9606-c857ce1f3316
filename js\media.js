// Media component functionality
class MediaComponent {
    constructor() {
        this.currentMode = 'video'; // 'video' or 'image'
        this.selectedVideo = null;
        this.selectedImages = [];
        this.selectedMusic = null;
        this.drawerOpen = false;
        this.drawerType = null; // 'video', 'image', 'audio'
        this.previewModalOpen = false;
        this.previewFile = null;
        this.uploading = false;
        
        // Available materials from backend
        this.materialVideoFiles = [];
        this.materialImageFiles = [];
        this.materialMusicFiles = [];
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateUI();
    }

    setupEventListeners() {
        // Mode switcher
        const modeButtons = document.querySelectorAll('#media-tab .mode-button[data-mode]');
        modeButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const mode = btn.dataset.mode;
                this.switchMode(mode);
            });
        });

        // File selection buttons
        const selectVideoBtn = document.getElementById('select-video-btn');
        const selectImagesBtn = document.getElementById('select-images-btn');
        const selectMusicBtn = document.getElementById('select-music-btn');

        selectVideoBtn?.addEventListener('click', () => {
            this.openFileDrawer('video');
        });

        selectImagesBtn?.addEventListener('click', () => {
            this.openFileDrawer('image');
        });

        selectMusicBtn?.addEventListener('click', () => {
            this.openFileDrawer('audio');
        });

        // Action buttons
        const clearMediaBtn = document.getElementById('clear-media-btn');
        const createMediaBtn = document.getElementById('create-media-btn');

        clearMediaBtn?.addEventListener('click', () => {
            this.handleClear();
        });

        createMediaBtn?.addEventListener('click', () => {
            this.handleCreate();
        });

        // Drawer close
        const closeDrawerBtn = document.getElementById('close-drawer');
        closeDrawerBtn?.addEventListener('click', () => {
            this.closeDrawer();
        });

        // Preview modal close
        const closePreviewBtn = document.getElementById('close-preview');
        closePreviewBtn?.addEventListener('click', () => {
            this.closePreviewModal();
        });

        // Close modals on overlay click
        const previewModal = document.getElementById('preview-modal');

        previewModal?.addEventListener('click', (e) => {
            if (e.target === previewModal) {
                this.closePreviewModal();
            }
        });
    }

    switchMode(mode) {
        this.currentMode = mode;
        
        // Update mode buttons
        const modeButtons = document.querySelectorAll('#media-tab .mode-button[data-mode]');
        const modeSwitcher = document.querySelector('#media-tab .mode-switcher');
        
        modeButtons.forEach((btn, index) => {
            btn.classList.toggle('active', btn.dataset.mode === mode);
            // Set data-active on the switcher to control animation
            if (btn.dataset.mode === mode && modeSwitcher) {
                modeSwitcher.setAttribute('data-active', index.toString());
            }
        });

        // Update mode content
        const modeContents = document.querySelectorAll('#media-tab .mode-content');
        modeContents.forEach(content => {
            content.classList.toggle('active', content.id === `${mode}-mode`);
        });

        this.updateActionButtons();
    }

    async openFileDrawer(type) {
        this.drawerType = type;
        await this.loadMaterialFilesByType(type);
        this.renderDrawerContent();
        
        const drawerTitle = document.getElementById('drawer-title');
        switch (type) {
            case 'video':
                drawerTitle.textContent = 'Select Video';
                break;
            case 'image':
                drawerTitle.textContent = 'Select Images';
                break;
            case 'audio':
                drawerTitle.textContent = 'Select Background Music';
                break;
        }
        
        Drawer.show('file-drawer');
        this.drawerOpen = true;
    }

    closeDrawer() {
        Drawer.hide('file-drawer');
        this.drawerOpen = false;
        this.drawerType = null;
    }

    async loadMaterialFilesByType(type) {
        try {
            const files = await backendApi.getMaterials(type);
            switch (type) {
                case 'video':
                    this.materialVideoFiles = files;
                    break;
                case 'image':
                    this.materialImageFiles = files;
                    break;
                case 'audio':
                    this.materialMusicFiles = files;
                    break;
            }
        } catch (error) {
            console.error(`Error loading ${type} files:`, error);
            Toast.error(`Failed to load ${type} files.`);
        }
    }

    renderDrawerContent() {
        const drawerBody = document.getElementById('drawer-body');
        
        switch (this.drawerType) {
            case 'video':
                this.renderVideoContent(drawerBody);
                break;
            case 'image':
                this.renderImageContent(drawerBody);
                break;
            case 'audio':
                this.renderMusicContent(drawerBody);
                break;
        }
    }

    renderVideoContent(container) {
        if (this.materialVideoFiles.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <p>No video files available.</p>
                    <p>Please upload video files in the Material section first.</p>
                </div>
            `;
            return;
        }

        container.innerHTML = `
            <div class="drawer-file-grid media-fixed">
                ${this.materialVideoFiles.map(file => this.createVideoCard(file)).join('')}
            </div>
        `;

        this.attachVideoCardListeners(container);
    }

    renderImageContent(container) {
        if (this.materialImageFiles.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <p>No image files available.</p>
                    <p>Please upload image files in the Material section first.</p>
                </div>
            `;
            return;
        }

        container.innerHTML = `
            <div class="drawer-file-grid media-fixed">
                ${this.materialImageFiles.map(file => this.createImageCard(file)).join('')}
            </div>
        `;

        this.attachImageCardListeners(container);
    }

    renderMusicContent(container) {
        if (this.materialMusicFiles.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <p>No music files available.</p>
                    <p>Please upload music files in the Material section first.</p>
                </div>
            `;
            return;
        }

        container.innerHTML = `
            <div class="drawer-file-grid audio-responsive">
                ${this.materialMusicFiles.map(file => this.createMusicCard(file)).join('')}
            </div>
        `;

        this.attachMusicCardListeners(container);
    }

    createVideoCard(file) {
        return `
            <div class="file-card" data-file-id="${file.file_name}">
                <div class="file-card-header">
                    <h3 class="file-card-title">${file.file_name}</h3>
                    <button class="file-card-delete-btn" data-action="delete">${Icons.RemoveIcon()}</button>
                </div>
                <div class="file-card-preview">
                    ${utils.createVideoPlayer(backendApi.getFileUrl(file.file_path), 'file-preview-video')}
                </div>
            </div>
        `;
    }

    createImageCard(file) {
        return `
            <div class="file-card" data-file-id="${file.file_name}">
                <div class="file-card-header">
                    <h3 class="file-card-title">${file.file_name}</h3>
                    <button class="file-card-delete-btn" data-action="delete">${Icons.RemoveIcon()}</button>
                </div>
                <div class="file-card-preview">
                    ${utils.createPreviewElement(file, file.file_name, 'file-preview-image')}
                </div>
            </div>
        `;
    }

    createMusicCard(file) {
        return `
            <div class="music-card" data-file-id="${file.file_name}">
                <div class="music-card-header">
                    <h3 class="music-card-title">${file.file_name}</h3>
                    <button class="file-card-delete-btn" data-action="delete">${Icons.RemoveIcon()}</button>
                </div>
                <div style="padding: 12px;">
                    ${utils.createCustomAudioPlayer(backendApi.getFileUrl(file.file_path))}
                </div>
            </div>
        `;
    }

    attachVideoCardListeners(container) {
        const cards = container.querySelectorAll('.file-card');
        cards.forEach(card => {
            const fileName = card.dataset.fileId;
            const deleteBtn = card.querySelector('[data-action="delete"]');
            
            card.addEventListener('click', (e) => {
                if (e.target.closest('[data-action="delete"]')) return;
                this.handleFileSelect(fileName, 'video');
            });

            deleteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.handlePermanentDelete(fileName, 'video');
            });
        });
    }

    attachImageCardListeners(container) {
        const cards = container.querySelectorAll('.file-card');
        cards.forEach(card => {
            const fileName = card.dataset.fileId;
            const deleteBtn = card.querySelector('[data-action="delete"]');
            
            card.addEventListener('click', (e) => {
                if (e.target.closest('[data-action="delete"]')) return;
                this.handleFileSelect(fileName, 'image');
            });

            deleteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.handlePermanentDelete(fileName, 'image');
            });
        });
    }

    attachMusicCardListeners(container) {
        const cards = container.querySelectorAll('.music-card');
        cards.forEach(card => {
            const fileName = card.dataset.fileId;
            const deleteBtn = card.querySelector('[data-action="delete"]');
            
            card.addEventListener('click', (e) => {
                if (e.target.closest('audio') || e.target.closest('[data-action="delete"]')) return;
                this.handleFileSelect(fileName, 'audio');
            });

            deleteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.handlePermanentDelete(fileName, 'audio');
            });
        });
    }



    handleFileSelect(fileName, type) {
        let file;
        switch (type) {
            case 'video':
                file = this.materialVideoFiles.find(f => f.file_name === fileName);
                break;
            case 'image':
                file = this.materialImageFiles.find(f => f.file_name === fileName);
                break;
            case 'audio':
                file = this.materialMusicFiles.find(f => f.file_name === fileName);
                break;
        }
        
        if (!file) return;

        const selectedFile = {
            id: file.file_name,
            name: file.file_name,
            url: backendApi.getFileUrl(file.file_path),
            filePath: file.file_path, // Keep original file path for backend API
            duration: file.duration_seconds ? utils.formatDuration(file.duration_seconds) : undefined,
            type: type === 'audio' ? 'audio' : type,
            size: file.file_size
        };

        switch (this.drawerType) {
            case 'video':
                this.selectedVideo = selectedFile;
                break;
            case 'image':
                this.selectedImages.push({ 
                    ...selectedFile, 
                    duration: '2.000' // Default 2 seconds duration per image
                });
                break;
            case 'audio':
                this.selectedMusic = selectedFile;
                break;
        }

        this.closeDrawer();
        this.updateSelectedFileLists();
        this.updateActionButtons();
        Toast.success(`${file.file_name} selected.`, 1000);
    }



    calculateTotalDuration() {
        if (this.selectedImages.length === 0) {
            return '0:00';
        }
        
        let totalDuration = 0;
        this.selectedImages.forEach(img => {
            const duration = utils.parseTimeString(img.duration || img.startTime || img.timestamp || '2.000');
            totalDuration += duration;
        });
        
        return utils.formatDuration(totalDuration);
    }

    handlePermanentDelete(fileName, type) {
        utils.confirm(
            'Delete File Permanently',
            `Are you sure you want to permanently delete "${fileName}"? This action cannot be undone.`,
            async () => {
                try {
                    const result = await backendApi.deleteMaterial(fileName, type);
                    
                    // Clear any selected files that reference the deleted file
                    this.clearDeletedFile(fileName, type);
                    
                    // Show different messages based on whether preview slots were affected
                    if (result.cleared_slots && result.cleared_slots.length > 0) {
                        Toast.success(`${fileName} deleted permanently. ${result.cleared_slots.length} preview slot(s) were also cleared.`, 2000);
                        
                        // Notify preview component if it exists to update its slots
                        if (window.previewComponent && typeof window.previewComponent.updateClearedSlots === 'function') {
                            await window.previewComponent.updateClearedSlots(result.cleared_slots);
                        }
                    } else {
                        Toast.success(`${fileName} deleted permanently.`, 1000);
                    }
                    
                    await this.loadMaterialFilesByType(type);
                    this.renderDrawerContent();
                } catch (error) {
                    console.error('Delete error:', error);
                    Toast.error(`Failed to delete ${fileName}.`);
                }
            }
        );
    }

    /**
     * Clear selected files that reference a deleted file
     * @param {string} fileName - Name of the deleted file
     * @param {string} fileType - Type of the deleted file ('video', 'image', 'audio')
     */
    clearDeletedFile(fileName, fileType) {
        let clearedFiles = [];
        
        // Check selected video
        if (this.selectedVideo && this.selectedVideo.name === fileName) {
            this.selectedVideo = null;
            clearedFiles.push('selected video');
        }
        
        // Check selected images
        const imagesBefore = this.selectedImages.length;
        this.selectedImages = this.selectedImages.filter(img => img.name !== fileName);
        if (this.selectedImages.length < imagesBefore) {
            clearedFiles.push(`${imagesBefore - this.selectedImages.length} selected image(s)`);
        }
        
        // Check selected music
        if (this.selectedMusic && this.selectedMusic.name === fileName) {
            this.selectedMusic = null;
            clearedFiles.push('selected music');
        }
        
        // Update UI if any files were cleared
        if (clearedFiles.length > 0) {
            this.updateSelectedFileLists();
            this.updateActionButtons();
            
            // Show notification about cleared selections
            const message = `File deleted. Also cleared: ${clearedFiles.join(', ')}.`;
            console.log(message);
        }
    }

    handleClear() {
        const hasSelection = this.selectedVideo || this.selectedImages.length > 0 || this.selectedMusic;
        if (!hasSelection) {
            Toast.info('No files to clear.');
            return;
        }

        utils.confirm(
            'Clear All Files',
            'Are you sure you want to clear all selected files?',
            () => {
                this.selectedVideo = null;
                this.selectedImages = [];
                this.selectedMusic = null;
                this.updateSelectedFileLists();
                this.updateActionButtons();
                Toast.success('All files cleared.', 1000);
            }
        );
    }

    async handleCreate() {
        const hasContent = this.currentMode === 'video'
            ? !!this.selectedVideo
            : this.selectedImages.length > 0;
        if (!hasContent || !this.selectedMusic) {
            Toast.warning('Please select both content and background music.');
            return;
        }

        this.uploading = true;
        this.updateActionButtons();

        try {
            if (this.currentMode === 'video' && this.selectedVideo && this.selectedMusic) {
                // Video + Music composition
                await backendApi.createVideoMedia(
                    this.selectedVideo.filePath,
                    0, // start time
                    5, // end time (5 seconds)
                    this.selectedMusic.filePath
                );
                Toast.success('Video media composition created successfully.', 1000);

                // Clear only video selection and shared music
                this.selectedVideo = null;
                this.selectedMusic = null;
            } else if (this.currentMode === 'image' && this.selectedImages.length > 0 && this.selectedMusic) {
                // Image slideshow + Music composition
                let currentTime = 0;
                const images = this.selectedImages.map(img => {
                    const duration = utils.parseTimeString(img.duration || img.startTime || img.timestamp || '2.000');
                    const startTime = currentTime;
                    const endTime = currentTime + duration;
                    currentTime = endTime;
                    
                    return {
                        file_path: img.filePath, // Use original file path, not display URL
                        start_time: startTime,
                        end_time: endTime
                    };
                });
                
                await backendApi.createImageMedia(images, this.selectedMusic.filePath);
                Toast.success('Image slideshow composition created successfully.', 1000);

                // Clear only image selections and shared music
                this.selectedImages = [];
                this.selectedMusic = null;
            }

            this.updateSelectedFileLists();
        } catch (error) {
            console.error('Create error:', error);
            Toast.error('Failed to create media composition.');
        } finally {
            this.uploading = false;
            this.updateActionButtons();
        }
    }

    updateSelectedFileLists() {
        this.updateSelectedVideo();
        this.updateSelectedImages();
        this.updateSelectedMusic();
    }

    updateSelectedVideo() {
        const container = document.getElementById('selected-video');
        if (!this.selectedVideo) {
            container.innerHTML = '';
            return;
        }

        container.innerHTML = this.createSelectedFileCard(this.selectedVideo);
        this.attachSelectedFileListeners(container);
    }

    updateSelectedImages() {
        const container = document.getElementById('selected-images');
        if (this.selectedImages.length === 0) {
            container.innerHTML = '';
            return;
        }

        const totalDuration = this.calculateTotalDuration();
        
        container.innerHTML = `
            <div class="image-timeline">
                <div class="timeline-header">
                    <h3 class="timeline-title">Image Timeline (${this.selectedImages.length} images, duration: ${totalDuration})</h3>
                    <p class="timeline-subtitle">Configure the timing and sequence of images for your slideshow presentation</p>
                </div>
                <div class="timeline-list">
                    ${this.selectedImages.map((img, index) => this.createTimelineItem(img, index)).join('')}
                </div>
            </div>
        `;

        this.attachTimelineListeners(container);
    }

    updateSelectedMusic() {
        const container = document.getElementById('selected-music');
        if (!this.selectedMusic) {
            container.innerHTML = '';
            return;
        }

        container.innerHTML = this.createSelectedFileCard(this.selectedMusic);
        this.attachSelectedFileListeners(container);
    }

    createSelectedFileCard(file) {
        return `
            <div class="selected-file-card" data-action="preview">
                <div class="file-item-info">
                    <span class="file-icon">${utils.getFileIcon(file.type)}</span>
                    <div class="file-details">
                        <div class="file-name">${utils.truncateMiddle(file.name, 30)}</div>
                        <div class="file-meta">
                            <span class="file-type">${file.type.toUpperCase()}</span>
                            <span class="separator">•</span>
                            <span class="file-size">${utils.formatFileSize(file.size)}</span>
                            ${file.duration ? `<span class="separator">•</span><span class="file-duration">${file.duration}</span>` : ''}
                        </div>
                    </div>
                </div>
                <div class="file-actions">
                    <button class="btn remove-button" data-action="delete" title="Remove">${Icons.CloseIcon()}</button>
                </div>
            </div>
        `;
    }

    createTimelineItem(image, index) {
        const duration = image.duration || image.startTime || image.timestamp || '2.000';
        const fileType = utils.getFileTypeFromName(image.name);
        
        return `
            <div class="timeline-item" data-index="${index}">
                <!-- Left Section: File Information -->
                <div class="timeline-file-section">
                    <div class="timeline-thumbnail">
                        <img src="${image.url}" alt="${image.name}" class="timeline-image" loading="lazy" onerror="window.utils.handleMediaError(this, 'timeline-image')">
                        <div class="timeline-index">#${index + 1}</div>
                    </div>
                    <div class="timeline-file-info">
                        <div class="timeline-file-name">${utils.truncateMiddle(image.name, 30)}</div>
                        <div class="timeline-file-meta">
                            <span class="file-size">${utils.formatFileSize(image.size)}</span>
                        </div>
                    </div>
                </div>
                
                <!-- Divider -->
                <div class="timeline-divider"></div>
                
                <!-- Right Section: Control Pane -->
                <div class="timeline-content-section">
                    <!-- Time Section -->
                    <div class="timeline-time-section">
                        <div class="time-group">
                            <label class="time-label">Duration</label>
                            <input type="text" class="time-input" value="${duration}" data-action="duration" placeholder="2.000">
                        </div>
                    </div>
                    
                    <!-- Controls -->
                    <div class="timeline-controls">
                        <button class="btn timeline-btn" data-action="up" ${index === 0 ? 'disabled' : ''} title="Move up">${Icons.ArrowUpIcon()}</button>
                        <button class="btn timeline-btn" data-action="down" ${index === this.selectedImages.length - 1 ? 'disabled' : ''} title="Move down">${Icons.ArrowDownIcon()}</button>
                        <button class="btn remove-button" data-action="delete" title="Remove">${Icons.CloseIcon()}</button>
                    </div>
                </div>
            </div>
        `;
    }

    attachSelectedFileListeners(container) {
        const fileCard = container.querySelector('.selected-file-card');
        const deleteBtn = container.querySelector('[data-action="delete"]');

        fileCard?.addEventListener('click', (e) => {
            if (e.target.closest('[data-action="delete"]')) return;
            
            // Determine which file to preview based on container ID
            let fileToPreview = null;
            if (container.id === 'selected-video' && this.selectedVideo) {
                fileToPreview = this.selectedVideo;
            } else if (container.id === 'selected-music' && this.selectedMusic) {
                fileToPreview = this.selectedMusic;
            } else if (container.id === 'selected-images') {
                // For images, we need to determine which image was clicked
                const cardIndex = Array.from(container.children).indexOf(fileCard);
                fileToPreview = this.selectedImages[cardIndex];
            }
            
            if (fileToPreview) {
                this.openPreviewModal(fileToPreview);
            }
        });

        deleteBtn?.addEventListener('click', (e) => {
            e.stopPropagation();
            if (container.id === 'selected-video' && this.selectedVideo) {
                this.selectedVideo = null;
                this.updateSelectedVideo();
            } else if (container.id === 'selected-music' && this.selectedMusic) {
                this.selectedMusic = null;
                this.updateSelectedMusic();
            }
            this.updateActionButtons();
            Toast.success('File removed from selection.', 1000);
        });
    }

    attachTimelineListeners(container) {
        const items = container.querySelectorAll('.timeline-item');
        items.forEach(item => {
            const index = parseInt(item.dataset.index);
            const durationInput = item.querySelector('[data-action="duration"]');
            const upBtn = item.querySelector('[data-action="up"]');
            const downBtn = item.querySelector('[data-action="down"]');
            const deleteBtn = item.querySelector('[data-action="delete"]');
            const thumbnail = item.querySelector('.timeline-thumbnail');

            // Click on file section to preview
            const fileSection = item.querySelector('.timeline-file-section');
            fileSection.addEventListener('click', (e) => {
                if (e.target.closest('button') || e.target.closest('input')) {
                    return;
                }
                const imageFile = this.selectedImages[index];
                if (imageFile) {
                    this.openPreviewModal(imageFile);
                }
            });

            durationInput.addEventListener('change', () => {
                this.selectedImages[index].duration = durationInput.value;
                // Remove old startTime and timestamp properties for consistency
                delete this.selectedImages[index].startTime;
                delete this.selectedImages[index].timestamp;
                // Update the timeline display to reflect new total duration
                this.updateSelectedImages();
            });

            upBtn?.addEventListener('click', (e) => {
                e.stopPropagation();
                this.moveImage(index, -1);
            });

            downBtn?.addEventListener('click', (e) => {
                e.stopPropagation();
                this.moveImage(index, 1);
            });

            deleteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.selectedImages.splice(index, 1);
                this.updateSelectedImages();
                this.updateActionButtons();
                Toast.success('Image removed from timeline.', 1000);
            });
        });
    }

    moveImage(index, direction) {
        const newIndex = index + direction;
        if (newIndex < 0 || newIndex >= this.selectedImages.length) return;

        const temp = this.selectedImages[index];
        this.selectedImages[index] = this.selectedImages[newIndex];
        this.selectedImages[newIndex] = temp;

        this.updateSelectedImages();
    }

    openPreviewModal(file) {
        if (!file) return;

        this.previewFile = file;
        const previewTitle = document.getElementById('preview-title');
        const previewBody = document.getElementById('preview-body');

        previewTitle.textContent = `${file.type.charAt(0).toUpperCase() + file.type.slice(1)} Preview`;
        
        if (file.type === 'image') {
            previewBody.innerHTML = `
                <div class="preview-content">
                    <img src="${file.url}" alt="${file.name}" style="max-width: 100%; max-height: 70vh; border-radius: 8px;">
                    <div class="preview-info">
                        <p><strong>File name:</strong> ${file.name}</p>
                        <p><strong>File size:</strong> ${utils.formatFileSize(file.size)}</p>
                        <p><strong>File type:</strong> ${file.type}</p>
                    </div>
                </div>
            `;
        } else if (file.type === 'video') {
            previewBody.innerHTML = `
                <div class="preview-content">
                    <video src="${file.url}" controls style="max-width: 100%; max-height: 70vh; border-radius: 8px;"></video>
                    <div class="preview-info">
                        <p><strong>File name:</strong> ${file.name}</p>
                        <p><strong>File size:</strong> ${utils.formatFileSize(file.size)}</p>
                        <p><strong>File type:</strong> ${file.type}</p>
                        ${file.duration ? `<p><strong>Duration:</strong> ${file.duration}</p>` : ''}
                    </div>
                </div>
            `;
        } else if (file.type === 'audio') {
            previewBody.innerHTML = `
                <div class="preview-content">
                    <div style="width: 100%;">
                        ${utils.createCustomAudioPlayer(file.url)}
                    </div>
                    <div class="preview-info">
                        <p><strong>File name:</strong> ${file.name}</p>
                        <p><strong>File size:</strong> ${utils.formatFileSize(file.size)}</p>
                        <p><strong>File type:</strong> ${file.type}</p>
                        ${file.duration ? `<p><strong>Duration:</strong> ${file.duration}</p>` : ''}
                    </div>
                </div>
            `;
        }

        Modal.show('preview-modal');
        this.previewModalOpen = true;
    }

    closePreviewModal() {
        Modal.hide('preview-modal');
        this.previewModalOpen = false;
        this.previewFile = null;
    }

    updateActionButtons() {
        const clearBtn = document.getElementById('clear-media-btn');
        const createBtn = document.getElementById('create-media-btn');
        
        // Calculate selection based on current mode
        let hasSelection, count;
        
        if (this.currentMode === 'video') {
            // Video mode: video + music
            hasSelection = this.selectedVideo || this.selectedMusic;
            count = [this.selectedVideo, this.selectedMusic].filter(f => f !== null).length;
        } else {
            // Image mode: images + music
            hasSelection = this.selectedImages.length > 0 || this.selectedMusic;
            count = this.selectedImages.length + (this.selectedMusic ? 1 : 0);
        }
        
        clearBtn.disabled = !hasSelection || this.uploading;
        createBtn.disabled = !hasSelection || this.uploading;
        
        if (this.uploading) {
            utils.setLoading(createBtn, true);
        } else {
            utils.setLoading(createBtn, false);
            createBtn.textContent = count > 0 ? `Create Media (${count})` : 'Create Media';
        }
    }

    updateUI() {
        this.updateSelectedFileLists();
        this.updateActionButtons();
    }
}

// Initialize Media component when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('media-tab')) {
        window.mediaComponent = new MediaComponent();
    }
}); 