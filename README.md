# LED Fans - Vanilla Version

This is a pure HTML/CSS/JavaScript version of the LED Fans, designed specifically for Raspberry Pi Zero compatibility without any external dependencies.

## Features

- **Material Upload**: Drag & drop file upload for images, videos, and music
- **Media Composition**: Create video/image slideshows with background music
- **Preview Management**: Manage 10 slots each for music and media compositions
- **No External Dependencies**: Pure vanilla JavaScript - no React, Antd, or other libraries
- **Raspberry Pi Compatible**: Designed to work on older browser versions

## File Structure

```
led-fans/
├── index.html              # Main application page
├── css/
│   ├── main.css            # Main styles (layout, components, modals)
│   ├── material.css        # Material upload component styles
│   ├── media.css           # Media composition component styles
│   └── preview.css         # Preview management component styles
├── js/
│   ├── api.js              # Backend API client
│   ├── utils.js            # Utility functions and helpers
│   ├── material.js         # Material upload component
│   ├── media.js            # Media composition component
│   ├── preview.js          # Preview management component
│   └── app.js              # Main app controller and tab navigation
└── README.md               # This file
```

## Setup Instructions

1. **Copy Files**: Copy the entire `led-fans` folder to your web server directory

2. **Backend Connection**: The app expects your Python backend server to be running on port 3002. Make sure your existing `server.py` is running:
   ```bash
   python server.py
   ```

3. **Access the App**: Open `index.html` in your browser or serve it through a web server:
   ```bash
   # Simple Python server (if needed)
   cd led-fans
   python -m http.server 8080
   ```

4. **LAN Access**: Other devices can access via your Pi's IP address:
   ```
   http://YOUR_PI_IP:8080
   ```

## Usage

### Material Tab
- **Upload Files**: Click upload areas or drag & drop files
- **Supported Formats**: 
  - Images: .jpeg, .jpg, .png
  - Videos: .mp4, .mov
  - Audio: .mp3, .m4a, .wav
- **Batch Upload**: Select multiple files and upload them together
- **File Management**: Remove files from pending list before upload

### Media Tab
- **Video Mode**: Select one video + background music
- **Image Mode**: Create slideshow with multiple images + timestamps + background music
- **File Selection**: Click placeholders to select from uploaded materials
- **Timeline Management**: Adjust image timestamps and order
- **Preview**: Preview selected files before creating compositions

### Preview Tab
- **Music Mode**: Manage 10 music slots
- **Media Mode**: Manage 10 media composition slots
- **Slot Management**: Click empty slots to add content, click filled slots to preview
- **Content Selection**: Choose from available materials and compositions

## Keyboard Shortcuts

- `Ctrl/Cmd + 1`: Switch to Material tab
- `Ctrl/Cmd + 2`: Switch to Media tab  
- `Ctrl/Cmd + 3`: Switch to Preview tab
- `Escape`: Close open modals/drawers

## Browser Compatibility

This version is designed to work on older browsers commonly found on Raspberry Pi Zero:
- Chrome 60+
- Firefox 55+
- Safari 10+
- Edge 16+

## Features Included

✅ **Complete UI Recreation**: All original functionality recreated in vanilla JS
✅ **Drag & Drop Upload**: Native HTML5 drag and drop
✅ **Audio Player**: Custom audio controls with progress tracking
✅ **Modal System**: Custom modal and drawer components
✅ **Toast Notifications**: Non-blocking notification system
✅ **File Management**: Full CRUD operations for materials and compositions
✅ **Timeline Editor**: Image slideshow timeline with timestamp management
✅ **Responsive Design**: Mobile-friendly layout
✅ **Error Handling**: Comprehensive error handling and user feedback

## Technical Details

- **No Build Process**: No compilation or bundling required
- **ES6+ Features**: Uses modern JavaScript features supported by target browsers
- **CSS Grid/Flexbox**: Modern CSS layout without external frameworks
- **Fetch API**: Native HTTP requests instead of Axios
- **Local Storage**: Browser storage for temporary data (if needed)
- **Memory Management**: Proper cleanup of audio players and event listeners

## Development Tools

When running on localhost, additional developer tools are available in the browser console:

```javascript
// Access components
dev.material  // Material component instance
dev.media     // Media component instance  
dev.preview   // Preview component instance
dev.api       // Backend API client

// Quick actions
dev.switchTab('media')           // Switch tabs
dev.showToast('Hello', 'info')   // Show notifications
dev.logState()                   // Log current app state
```

## API Compatibility

This version uses the same backend API as the React version:
- `POST /api/materials` - Upload files
- `GET /api/materials?file_type=audio` - Get materials by type
- `DELETE /api/materials` - Delete materials
- `POST /api/medias/video` - Create video compositions
- `POST /api/medias/image` - Create image compositions
- `GET /api/medias` - Get media compositions
- `GET/PUT/DELETE /api/slots/:type/:id` - Manage preview slots

## Performance Optimizations

- **Lazy Loading**: Components load materials only when needed
- **Audio Management**: Centralized audio player with automatic cleanup
- **Memory Efficient**: Proper event listener cleanup and DOM management
- **Minimal DOM Updates**: Efficient rendering with targeted updates
- **Image Optimization**: Responsive images with proper sizing

## Troubleshooting

1. **Backend Connection Issues**: Check that your Python server is running on port 3002
2. **File Upload Failures**: Verify file types and sizes are within limits
3. **Audio Playback Issues**: Ensure audio files are in supported formats
4. **Responsive Issues**: Test on actual Pi Zero browser, not just desktop simulation

## Migration Notes

This vanilla version provides identical functionality to the React version but:
- Removes all React/Antd dependencies
- Uses native browser APIs instead of external libraries
- Maintains the same user experience and workflow
- Compatible with older browser versions on Raspberry Pi Zero

The backend Python server remains unchanged, so you can switch between React and vanilla versions seamlessly. 