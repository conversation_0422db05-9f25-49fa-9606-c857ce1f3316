/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

/* App Container */
.app-container {
    max-width: 800px;
    min-width: 320px;
    margin: 0 auto;
    background-color: white;
    min-height: 100vh;
}

/* Tab Navigation */
.tab-nav {
    display: flex;
    background-color: #fff;
    border-bottom: 2px solid #e8e8e8;
    padding: 0;
    position: relative;
}

.tab-nav::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: calc(100% / 3);
    height: 2px;
    background-color: #1890ff;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(0%);
    z-index: 1;
}

.tab-nav[data-active="media"]::after {
    transform: translateX(100%);
}

.tab-nav[data-active="preview"]::after {
    transform: translateX(200%);
}

.tab-button {
    flex: 1;
    background: none;
    border: none;
    padding: 16px 24px;
    cursor: pointer;
    font-size: 18px;
    color: #666;
    transition: color 0.3s ease;
    position: relative;
}

.tab-button:hover {
    color: #1890ff;
}

.tab-button.active {
    color: #1890ff;
}

/* Tab Content */
.tab-content {
    padding: 24px 24px 48px;
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
}

/* Common Buttons */
.btn {
    padding: 8px 16px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background-color: #fff;
    color: #333;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn:hover:not(:disabled) {
    border-color: #40a9ff;
    color: #40a9ff;
}

.btn:disabled {
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    color: #bfbfbf;
    cursor: not-allowed;
}

.btn-primary {
    background-color: #1890ff;
    border-color: #1890ff;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: #40a9ff;
    border-color: #40a9ff;
    color: white;
}

.btn-secondary {
    background-color: #fff;
    border-color: #d9d9d9;
    color: #666;
}

.btn-danger {
    background-color: #ff4d4f;
    border-color: #ff4d4f;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background-color: #ff7875;
    border-color: #ff7875;
}

/* Upload Areas */
.upload-area {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    padding: 18px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #fafafa;
}

.upload-area:hover {
    border-color: #1890ff;
    background-color: #eef3f7;
}

.upload-area.dragover {
    border-color: #1890ff;
    background-color: #e6f7ff;
}

.upload-icon {
    font-size: 48px;
}

.upload-text {
    font-size: 16px;
    color: #333;
}

.upload-hint {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
}

/* Upload Placeholder */
.upload-placeholder {
    width: 100%;
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    padding: 32px;
    background: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.upload-placeholder:hover {
    border-color: #1890ff;
    background-color: #f0f9ff;
}

.placeholder-icon {
    font-size: 48px;
}

.placeholder-text {
    font-size: 16px;
    color: #333;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 12px;
    justify-content: space-between;
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid #e8e8e8;
}

.action-buttons .btn {
    flex: 1;
    font-size: 15px;
    font-weight: 500;
    justify-content: center;
    text-align: center;
    min-height: 40px;
}

/* Mode Switcher */
.mode-switcher {
    display: flex;
    background-color: #f1f2f4;
    border-radius: 12px;
    padding: 6px;
    margin-bottom: 24px;
    position: relative;
    width: 100%;
}

.mode-switcher::before {
    content: '';
    position: absolute;
    top: 6px;
    left: 6px;
    width: calc(50% - 6px);
    height: calc(100% - 12px);
    background: white;
    border-radius: 8px;
    box-shadow: 
        0 1px 3px rgba(0, 0, 0, 0.12), 
        0 1px 2px rgba(0, 0, 0, 0.08),
        0 0 0 1px rgba(255, 255, 255, 0.05);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
}

.mode-switcher[data-active="1"]::before {
    transform: translateX(100%);
}

.mode-button {
    flex: 1;
    padding: 12px 16px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 15px;
    font-weight: 600;
    color: #6b7280;
    transition: color 0.3s ease;
    position: relative;
    z-index: 2;
    white-space: nowrap;
    min-height: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.mode-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mode-icon svg {
    width: 20px;
    height: 20px;
}

.mode-button:hover {
    color: #4b5563;
}

.mode-button.active {
    color: #374151;
}

.mode-content {
    display: none;
}

.mode-content.active {
    display: block;
}

/* Bottom Drawer */
.drawer-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.drawer-backdrop.open {
    opacity: 1;
    visibility: visible;
}

.bottom-drawer {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%) translateY(100%);
    max-width: 800px;
    min-width: 320px;
    width: 100%;
    padding: 16px;
    overflow-y: auto;
    background-color: white;
    border-radius: 8px 8px 0 0;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    height: 80vh;
}

.drawer-backdrop.open .bottom-drawer {
    transform: translateX(-50%) translateY(0);
}

.drawer-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.drawer-title {
    display: inline-flex;
    align-items: center;
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    gap: 8px;
}

.drawer-icon {
    width: 16px;
    height: 16px;
    color: #6b7280;
}

.drawer-close-btn {
    background: transparent;
    border: none;
    cursor: pointer;
    color: #9ca3af;
    padding: 4px;
    border-radius: 6px;
    width: 32px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.drawer-close-btn:hover {
    background-color: #f3f4f6;
    color: #374151;
}

.close-icon {
    width: 12px;
    height: 12px;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.drawer-body {
    margin-bottom: 24px;
    max-width: none;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1100;
}

.modal.open {
    display: flex;
}

.modal-content {
    background-color: white;
    border-radius: 8px;
    max-width: 90vw;
    max-height: 90vh;
    width: 600px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.modal-header {
    padding: 16px 24px;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.modal-body {
    padding: 24px;
    overflow-y: auto;
    flex: 1;
}

.close-btn {
    background: transparent;
    border: none;
    cursor: pointer;
    color: #9ca3af;
    padding: 4px;
    border-radius: 6px;
    width: 32px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background-color: #f3f4f6;
    color: #374151;
}

.close-btn svg {
    width: 14px;
    height: 14px;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1200;
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.toast {
    background-color: white;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    padding: 12px 16px;
    min-width: 320px;
    max-width: 400px;
    display: flex;
    align-items: center;
    gap: 12px;
    animation: slideIn 0.3s ease;
}


.toast-icon {
    font-size: 16px;
    font-weight: 600;
    min-width: 16px;
}

.toast-message {
    flex: 1;
    font-size: 15px;
    font-weight: 500;
    color: #333;
    line-height: 1.4;
}

.toast.success .toast-icon::before { content: '✓'; color: #52c41a; }
.toast.error .toast-icon::before { content: '✗'; color: #ff4d4f; }
.toast.warning .toast-icon::before { content: '⚠'; color: #faad14; }
.toast.info .toast-icon::before { content: 'ℹ'; color: #1890ff; }

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Loading State */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #1890ff;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    animation: spin 1s linear infinite;
    display: inline-block;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .app-container {
        margin: 0;
        box-shadow: none;
    }
    
    .tab-content {
        padding: 16px 16px 32px;
    }
    
    .drawer-header {
        margin-bottom: 12px;
    }
    
    .drawer-body {
        margin-bottom: 16px;
    }
    
    .modal-content {
        margin: 20px;
    }
    
    .tab-nav {
        padding: 0;
    }
    
    .tab-button {
        padding: 12px 16px;
        font-size: 16px;
    }
    
    .action-buttons .btn {
        font-size: 14px;
        padding: 10px 16px;
        min-height: 44px;
    }
    
    .toast {
        min-width: 280px;
        max-width: calc(100vw - 40px);
    }
    
    .toast-container {
        right: 20px;
    }
    
    .mode-button {
        font-size: 14px;
        padding: 10px 12px;
        gap: 6px;
        min-height: 40px;
    }
    
    .mode-switcher {
        padding: 4px;
        border-radius: 10px;
    }
    
    .mode-switcher::before {
        top: 4px;
        left: 4px;
        width: calc(50% - 4px);
        height: calc(100% - 8px);
        border-radius: 6px;
    }
}

/* Custom Audio Player Styles */
.custom-audio-player {
    --media-background-color: transparent;
    --media-control-background: transparent;
    --media-control-hover-background: transparent;
    --media-accent-color: #1890ff;
    --media-focus-box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.5);
    width: 100%;
}

/* Control Bar */
.custom-audio-player media-control-bar {
    width: 100%;
    height: 64px;
    padding: 0 16px;
    background: white;
    display: flex;
    align-items: center;
    gap: 12px;
}

/* Control Panel */
.custom-audio-player .control-panel {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex: 0 0 33.33%;
    max-width: 200px;
    min-width: 140px;
}

/* Buttons */
.custom-audio-player media-seek-backward-button,
.custom-audio-player media-seek-forward-button {
    padding: 0;
}

.custom-audio-player media-seek-backward-button div,
.custom-audio-player media-seek-forward-button div {
    display: flex;
    align-items: center;
    justify-content: center;
}

.custom-audio-player media-play-button {
    height: 40px;
    width: 40px;
    padding: 8px;
    border-radius: 50%;
    background: #475569;
}

.custom-audio-player media-play-button div {
    display: flex;
    align-items: center;
    justify-content: center;
}

.custom-audio-player media-play-button div[slot="play"] {
    position: relative;
    left: 1px;
}

.custom-audio-player media-mute-button {
    min-width: 24px;
}

.custom-audio-player media-mute-button div {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Desktop Elements */
.custom-audio-player .desktop-separator {
    height: 60%;
}

.custom-audio-player .desktop-time,
.custom-audio-player .desktop-duration {
    color: #64748b;
    font-size: 14px;
    min-width: 40px;
}

.custom-audio-player .desktop-progress-bar {
    display: block;
    height: 8px;
    min-height: 0;
    padding: 0;
    border-radius: 4px;
    background: #f1f5f9;
    flex: 1;
    min-width: 120px;
    --media-range-track-background: transparent;
    --media-time-range-buffered-color: rgba(0, 0, 0, 0.02);
    --media-range-bar-color: #1890ff;
    --media-range-track-border-radius: 4px;
    --media-range-track-height: 8px;
    --media-range-thumb-background: #1890ff;
    --media-range-thumb-box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.9);
    --media-range-thumb-width: 4px;
    --media-range-thumb-height: 16px;
    --media-preview-time-text-shadow: transparent;
}

.custom-audio-player .desktop-progress-bar media-preview-time-display {
    color: #64748b;
    font-size: 12px;
}

/* Mobile Elements (Hidden by Default) */
.custom-audio-player .mobile-progress-bar {
    display: none;
    width: 100%;
    height: 8px;
    min-height: 0;
    padding: 0;
    margin-bottom: 12px;
    border-radius: 4px;
    background: #f1f5f9;
    --media-range-track-background: transparent;
    --media-time-range-buffered-color: rgba(0, 0, 0, 0.02);
    --media-range-bar-color: #1890ff;
    --media-range-track-border-radius: 4px;
    --media-range-track-height: 8px;
    --media-range-thumb-background: #1890ff;
    --media-range-thumb-box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.9);
    --media-range-thumb-width: 4px;
    --media-range-thumb-height: 16px;
    --media-preview-time-text-shadow: transparent;
}

.custom-audio-player .mobile-progress-bar media-preview-time-display {
    color: #64748b;
    font-size: 12px;
}

.custom-audio-player .mobile-time-info {
    display: none;
}

.custom-audio-player .mobile-time-info .time-separator {
    margin: 0 6px;
    color: #4b5563;
}

/* Button hover effects */
.custom-audio-player media-seek-backward-button:hover,
.custom-audio-player media-seek-forward-button:hover,
.custom-audio-player media-mute-button:hover {
    opacity: 0.7;
}

.custom-audio-player media-play-button:hover {
    background: #374151;
}

/* Focus styles for accessibility */
.custom-audio-player media-seek-backward-button:focus,
.custom-audio-player media-seek-forward-button:focus,
.custom-audio-player media-play-button:focus,
.custom-audio-player media-mute-button:focus {
    outline: 2px solid #1890ff;
    outline-offset: 2px;
}

/* Time range hover effect */
.custom-audio-player media-time-range:hover {
    --media-range-thumb-box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.9);
}

/* Mobile responsive design - Vertical Layout */
@media (max-width: 480px) {
    /* Mobile container */
    .custom-audio-player {
        padding: 12px;
        background: white;
    }
    
    /* Show mobile elements */
    .custom-audio-player .mobile-progress-bar {
        display: block;
    }
    
    .custom-audio-player .mobile-time-info {
        display: flex;
        flex: 1;
        justify-content: center;
        align-items: center;
    }
    
    .custom-audio-player .mobile-time-info .time-separator {
        margin: 0 6px;
    }
    
    /* Hide desktop elements */
    .custom-audio-player .desktop-progress-bar,
    .custom-audio-player .desktop-separator,
    .custom-audio-player .desktop-time,
    .custom-audio-player .desktop-duration {
        display: none;
    }
    
    /* Control bar adjustments */
    .custom-audio-player media-control-bar {
        height: 48px;
        padding: 0;
        background: transparent;
        box-shadow: none;
        border: none;
        border-radius: 0;
    }
    
    /* Control panel adjustments */
    .custom-audio-player .control-panel {
        flex: 0 0 140px;
        max-width: 140px;
    }
} 