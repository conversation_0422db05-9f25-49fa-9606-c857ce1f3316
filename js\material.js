// Material component functionality
class MaterialComponent {
    constructor() {
        this.pendingImageVideoFiles = [];
        this.pendingAudioFiles = [];
        this.uploading = false;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupDragAndDrop();
        this.updateUI();
    }

    setupEventListeners() {
        // File input change handlers
        const videoFileInput = document.getElementById('video-file-input');
        const audioFileInput = document.getElementById('audio-file-input');
        
        videoFileInput.addEventListener('change', (e) => {
            this.handleVideoFilesSelected(Array.from(e.target.files));
            e.target.value = ''; // Reset input
        });
        
        audioFileInput.addEventListener('change', (e) => {
            this.handleAudioFilesSelected(Array.from(e.target.files));
            e.target.value = ''; // Reset input
        });

        // Upload area click handlers
        const videoUploadArea = document.getElementById('video-upload-area');
        const audioUploadArea = document.getElementById('audio-upload-area');
        
        videoUploadArea.addEventListener('click', () => {
            videoFileInput.click();
        });
        
        audioUploadArea.addEventListener('click', () => {
            audioFileInput.click();
        });

        // Action button handlers
        const clearPendingBtn = document.getElementById('clear-pending-btn');
        const uploadBtn = document.getElementById('upload-btn');
        
        clearPendingBtn.addEventListener('click', () => {
            this.handleClearPending();
        });
        
        uploadBtn.addEventListener('click', () => {
            this.handleUpload();
        });
    }

    setupDragAndDrop() {
        const videoUploadArea = document.getElementById('video-upload-area');
        const audioUploadArea = document.getElementById('audio-upload-area');
        
        // Video/Image drag and drop
        utils.setupDragAndDrop(
            videoUploadArea,
            (files) => this.handleVideoFilesSelected(files),
            ['image/jpeg', 'image/jpg', 'image/png', 'video/mp4', 'video/quicktime']
        );
        
        // Audio drag and drop
        utils.setupDragAndDrop(
            audioUploadArea,
            (files) => this.handleAudioFilesSelected(files),
            ['audio/mpeg', 'audio/mp3', 'audio/mp4', 'audio/x-m4a', 'audio/wav', 'audio/wave']
        );
    }

    handleVideoFilesSelected(files) {
        const validFiles = [];
        const invalidFiles = [];
        
        files.forEach(file => {
            const allowedTypes = [
                'image/jpeg',
                'image/jpg', 
                'image/png',
                'video/mp4',
                'video/quicktime'
            ];
            
            if (allowedTypes.includes(file.type)) {
                const uploadFile = {
                    uid: `${Date.now()}-${Math.random()}`,
                    name: file.name,
                    type: file.type,
                    size: file.size,
                    file: file,
                    status: 'pending'
                };
                validFiles.push(uploadFile);
            } else {
                invalidFiles.push(file.name);
            }
        });
        
        if (invalidFiles.length > 0) {
            Toast.error(`Invalid file types: ${invalidFiles.join(', ')}. Only .jpeg, .jpg, .png, .mp4, .mov files are allowed.`);
        }
        
        if (validFiles.length > 0) {
            this.pendingImageVideoFiles.push(...validFiles);
            this.updateUI();
            Toast.success(`${validFiles.length} file(s) added to pending list.`, 1000);
        }
    }

    handleAudioFilesSelected(files) {
        const validFiles = [];
        const invalidFiles = [];
        
        files.forEach(file => {
            const allowedTypes = [
                'audio/mpeg',
                'audio/mp3',
                'audio/mp4',
                'audio/x-m4a',
                'audio/wav',
                'audio/wave'
            ];
            
            if (allowedTypes.includes(file.type)) {
                const uploadFile = {
                    uid: `${Date.now()}-${Math.random()}`,
                    name: file.name,
                    type: file.type,
                    size: file.size,
                    file: file,
                    status: 'pending'
                };
                validFiles.push(uploadFile);
            } else {
                invalidFiles.push(file.name);
            }
        });
        
        if (invalidFiles.length > 0) {
            Toast.error(`Invalid file types: ${invalidFiles.join(', ')}. Only .mp3, .m4a, .wav files are allowed.`);
        }
        
        if (validFiles.length > 0) {
            this.pendingAudioFiles.push(...validFiles);
            this.updateUI();
            Toast.success(`${validFiles.length} file(s) added to pending list.`, 1000);
        }
    }

    handleRemoveFile(fileUid, isAudio = false) {
        if (isAudio) {
            this.pendingAudioFiles = this.pendingAudioFiles.filter(file => file.uid !== fileUid);
        } else {
            this.pendingImageVideoFiles = this.pendingImageVideoFiles.filter(file => file.uid !== fileUid);
        }
        this.updateUI();
        Toast.success('File removed from pending list.', 1000);
    }

    handleClearPending() {
        const totalPending = this.pendingImageVideoFiles.length + this.pendingAudioFiles.length;
        if (totalPending === 0) {
            Toast.info('No pending files to clear.');
            return;
        }

        utils.confirm(
            'Clear Pending Files',
            `Are you sure you want to clear all ${totalPending} pending file(s)?`,
            () => {
                this.pendingImageVideoFiles = [];
                this.pendingAudioFiles = [];
                this.updateUI();
                Toast.success('All pending files cleared.', 1000);
            }
        );
    }

    async handleUpload() {
        const allFiles = [...this.pendingImageVideoFiles, ...this.pendingAudioFiles];
        if (allFiles.length === 0) {
            Toast.warning('Please select files to upload.');
            return;
        }

        this.uploading = true;
        this.updateUI();
        
        let successCount = 0;
        let errorCount = 0;

        try {
            for (const uploadFile of allFiles) {
                try {
                    await backendApi.uploadMaterial(uploadFile.file);
                    successCount++;
                } catch (error) {
                    console.error(`Error uploading ${uploadFile.name}:`, error);
                    errorCount++;
                }
            }

            // Clear pending files after upload
            this.pendingImageVideoFiles = [];
            this.pendingAudioFiles = [];

            if (errorCount === 0) {
                Toast.success(`All ${successCount} file(s) uploaded successfully.`, 1000);
            } else {
                Toast.warning(`${successCount} file(s) uploaded successfully, ${errorCount} failed.`);
            }
        } finally {
            this.uploading = false;
            this.updateUI();
        }
    }

    updateUI() {
        this.updateCounters();
        this.updatePendingFileLists();
        this.updateActionButtons();
    }

    updateCounters() {
        const videoPendingCount = document.getElementById('video-pending-count');
        const audioPendingCount = document.getElementById('audio-pending-count');
        
        videoPendingCount.textContent = this.pendingImageVideoFiles.length;
        audioPendingCount.textContent = this.pendingAudioFiles.length;
    }

    updatePendingFileLists() {
        this.updateImageVideoFileList();
        this.updateAudioFileList();
    }

    updateImageVideoFileList() {
        const pendingImages = this.pendingImageVideoFiles.filter(file => file.type.startsWith('image/'));
        const pendingVideos = this.pendingImageVideoFiles.filter(file => file.type.startsWith('video/'));
        
        this.renderFileGroup('pending-images', 'Images', pendingImages, false);
        this.renderFileGroup('pending-videos', 'Videos', pendingVideos, false);
    }

    updateAudioFileList() {
        this.renderFileGroup('pending-audio', 'Music', this.pendingAudioFiles, true);
    }

    renderFileGroup(containerId, title, files, isAudio) {
        const container = document.getElementById(containerId);
        
        if (files.length === 0) {
            container.style.display = 'none';
            return;
        }
        
        container.style.display = 'block';
        
        // Update summary
        const summary = container.querySelector('.file-group-summary');
        const totalSize = files.reduce((sum, file) => sum + file.size, 0);
        summary.textContent = `${files.length} files, ${utils.formatFileSize(totalSize)}`;
        
        // Update content
        const content = container.querySelector('.file-group-content');
        content.innerHTML = '';
        
        files.forEach(file => {
            content.appendChild(this.createFileItem(file, isAudio));
        });
    }

    createFileItem(file, isAudio) {
        const item = document.createElement('div');
        item.className = 'file-item';
        
        const fileType = backendApi.getFileTypeFromMime(file.type);
        const fileTypeLabel = file.type.split('/')[1]?.toUpperCase() || 'FILE';
        
        item.innerHTML = `
            <div class="file-item-info">
                <span class="file-icon">${utils.getFileIcon(file.type)}</span>
                <div class="file-details">
                    <div class="file-name">${utils.truncateMiddle(file.name, 30)}</div>
                    <div class="file-meta">
                        <span class="file-type">${fileTypeLabel}</span>
                        <span class="separator">•</span>
                        <span class="file-size">${utils.formatFileSize(file.size)}</span>
                    </div>
                </div>
            </div>
            <div class="file-actions">
            <button class="btn remove-button" title="Remove">
                ${Icons.CloseIcon()}
            </button>
            </div>
        `;
        
        // Add remove handler
        const removeBtn = item.querySelector('.remove-button');
        removeBtn.addEventListener('click', () => {
            this.handleRemoveFile(file.uid, isAudio);
        });
        
        return item;
    }

    updateActionButtons() {
        const clearPendingBtn = document.getElementById('clear-pending-btn');
        const uploadBtn = document.getElementById('upload-btn');
        
        const totalPending = this.pendingImageVideoFiles.length + this.pendingAudioFiles.length;
        
        // Update button states
        clearPendingBtn.disabled = totalPending === 0 || this.uploading;
        uploadBtn.disabled = totalPending === 0 || this.uploading;
        
        // Update button text
        clearPendingBtn.textContent = `Clear Pending (${totalPending})`;
        
        if (this.uploading) {
            utils.setLoading(uploadBtn, true);
        } else {
            utils.setLoading(uploadBtn, false);
            uploadBtn.textContent = totalPending > 0 ? `Upload (${totalPending})` : 'Upload';
        }
    }
}

// Initialize Material component when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('material-tab')) {
        window.materialComponent = new MaterialComponent();
    }
}); 