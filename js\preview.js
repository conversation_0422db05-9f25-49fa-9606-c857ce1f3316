// Preview component functionality
class PreviewComponent {
    constructor() {
        this.currentMode = 'music'; // 'music' or 'media'
        this.musicSlots = [];
        this.mediaSlots = [];
        this.drawerOpen = false;
        this.drawerType = null; // 'music' or 'media'
        this.selectedSlotId = null;
        
        // Available content for selection
        this.availableMusicFiles = [];
        this.availableMediaCompositions = [];
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadPreviewData();
        this.loadAvailableContent();
    }

    setupEventListeners() {
        // Mode switcher
        const modeButtons = document.querySelectorAll('#preview-tab .mode-button[data-mode]');
        modeButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const mode = btn.dataset.mode;
                this.switchMode(mode);
            });
        });

        // Drawer close
        const closeDrawerBtn = document.getElementById('close-drawer');
        closeDrawerBtn?.addEventListener('click', () => {
            this.closeDrawer();
        });
    }

    switchMode(mode) {
        this.currentMode = mode;
        
        // Update mode buttons
        const modeButtons = document.querySelectorAll('#preview-tab .mode-button[data-mode]');
        const modeSwitcher = document.querySelector('#preview-tab .mode-switcher');
        
        modeButtons.forEach((btn, index) => {
            btn.classList.toggle('active', btn.dataset.mode === mode);
            // Set data-active on the switcher to control animation
            if (btn.dataset.mode === mode && modeSwitcher) {
                modeSwitcher.setAttribute('data-active', index.toString());
            }
        });

        // Update slots grid class
        const slotsGrid = document.getElementById('preview-slots');
        slotsGrid.className = `slots-grid ${mode}-mode`;

        this.renderSlots();
    }

    async loadPreviewData() {
        try {
            const [musicSlotsData, mediaSlotsData] = await Promise.all([
                backendApi.getSlots('music'),
                backendApi.getSlots('media')
            ]);

            // Transform backend slot data to component format
            this.musicSlots = musicSlotsData.map((slot, index) => ({
                id: index,
                title: `Music Slot ${index + 1}`,
                mode: 'music',
                isEmpty: !slot.music_file_path,
                content: slot
            }));

            this.mediaSlots = mediaSlotsData.map((slot, index) => ({
                id: index,
                title: `Media Slot ${index + 1}`,
                mode: 'media',
                isEmpty: !slot.video_file_path || !slot.music_file_path,
                content: slot
            }));
        } catch (error) {
            console.error('Error loading preview data:', error);
            // Initialize with empty slots
            this.musicSlots = this.createDefaultSlots('music');
            this.mediaSlots = this.createDefaultSlots('media');
        }

        this.renderSlots();
    }

    createDefaultSlots(mode) {
        const count = 10;
        return Array.from({ length: count }, (_, index) => ({
            id: index,
            title: `${mode.charAt(0).toUpperCase() + mode.slice(1)} Slot ${index + 1}`,
            mode,
            isEmpty: true,
            content: undefined
        }));
    }

    async loadAvailableContent() {
        try {
            const [musicFiles, mediaCompositions] = await Promise.all([
                backendApi.getMaterials('audio'),
                backendApi.getMedias()
            ]);
            this.availableMusicFiles = musicFiles;
            this.availableMediaCompositions = Object.values(mediaCompositions);
        } catch (error) {
            console.error('Error loading available content:', error);
        }
    }

    async loadAvailableContentByType(type) {
        try {
            if (type === 'music') {
                const musicFiles = await backendApi.getMaterials('audio');
                this.availableMusicFiles = musicFiles;
            } else {
                const mediaCompositions = await backendApi.getMedias();
                this.availableMediaCompositions = Object.values(mediaCompositions);
            }
        } catch (error) {
            console.error(`Error loading ${type} content:`, error);
        }
    }

    renderSlots() {
        const slotsGrid = document.getElementById('preview-slots');
        const currentSlots = this.currentMode === 'music' ? this.musicSlots : this.mediaSlots;
        
        slotsGrid.innerHTML = currentSlots.map(slot => this.createSlotCard(slot)).join('');
        this.attachSlotListeners();
    }

    createSlotCard(slot) {
        if (slot.isEmpty) {
            return `
                <div class="slot-card empty upload-placeholder" data-slot-id="${slot.id}">
                    <div class="placeholder-icon">${Icons.AddIcon()}</div>
                    <p class="placeholder-text">Click to add ${this.currentMode}</p>
                </div>
            `;
        } else {
            const duration = this.getSlotDuration(slot);
            
            // For music slots, show audio player instead of preview image
            if (slot.mode === 'music' && slot.content?.music_file_path) {
                const audioUrl = backendApi.getFileUrl(slot.content.music_file_path);
                const fileName = slot.content.music_file_path.split('/').pop();
                return `
                    <div class="slot-card ${slot.mode}-slot" data-slot-id="${slot.id}">
                        <div class="file-card-header">
                            <h3 class="file-card-title">${slot.title}</h3>
                            <div class="slot-header-actions">
                                <button class="slot-play-btn" data-action="play" title="Play">
                                    ${Icons.PlayIcon()}
                                    <span class="button-label">Play</span>
                                </button>
                                <button class="slot-close-btn" data-action="remove">${Icons.CloseIcon()}</button>
                            </div>
                        </div>
                        <div class="slot-audio-content">
                            ${utils.createCustomAudioPlayer(audioUrl)}
                        </div>
                        <div class="file-card-meta">
                            <span class="file-name">${utils.truncateMiddle(fileName, 25)}</span>
                        </div>
                    </div>
                `;
            } else {
                // For media slots, show video player + audio player 
                const fileName = slot.content?.video_file_path ? slot.content.video_file_path.split('/').pop() : '';
                
                // Create video player for the video part of the media slot
                const videoUrl = slot.content?.video_file_path ? 
                    backendApi.getFileUrl(slot.content.video_file_path) : null;
                
                // Create audio player for the music part of the media slot
                const audioUrl = slot.content?.music_file_path ? 
                    backendApi.getFileUrl(slot.content.music_file_path) : null;
                
                return `
                    <div class="slot-card ${slot.mode}-slot" data-slot-id="${slot.id}">
                        <div class="file-card-header">
                            <h3 class="file-card-title">${slot.title}</h3>
                            <div class="slot-header-actions">
                                <button class="slot-play-btn" data-action="play" title="Play">
                                    ${Icons.PlayIcon()}
                                    <span class="button-label">Play</span>
                                </button>
                                <button class="slot-close-btn" data-action="remove">${Icons.CloseIcon()}</button>
                            </div>
                        </div>
                        <div class="file-card-preview">
                            ${utils.createVideoPlayer(videoUrl, 'file-preview-video')}
                        </div>
                        ${audioUrl ? `
                            <div class="media-card-audio">
                                ${utils.createCustomAudioPlayer(audioUrl)}
                            </div>
                        ` : `
                            <div class="file-card-meta">
                                <span class="file-name">${utils.truncateMiddle(fileName, 25)}</span>
                            </div>
                        `}
                    </div>
                `;
            }
        }
    }



    getSlotDuration(slot) {
        // For now, return placeholder duration
        return this.currentMode === 'music' ? '3:45' : '0:05';
    }

    attachSlotListeners() {
        const slotCards = document.querySelectorAll('.slot-card');
        slotCards.forEach(card => {
            const slotId = parseInt(card.dataset.slotId);
            const removeBtn = card.querySelector('[data-action="remove"]');
            const playBtn = card.querySelector('[data-action="play"]');
            
            card.addEventListener('click', (e) => {
                // Prevent action when clicking on audio controls, video controls, play button, or remove button
                if (e.target.closest('[data-action="remove"]') || 
                    e.target.closest('[data-action="play"]') ||
                    e.target.closest('audio') || 
                    e.target.closest('video') ||
                    e.target.closest('.custom-audio-player') ||
                    e.target.closest('.media-card-audio') ||
                    e.target.closest('.file-preview-video')) return;
                
                if (card.classList.contains('empty')) {
                    this.handleAddContent(slotId);
                }
            });

            removeBtn?.addEventListener('click', (e) => {
                e.stopPropagation();
                this.handleRemoveSlot(slotId);
            });

            playBtn?.addEventListener('click', (e) => {
                e.stopPropagation();
                this.handlePlaySlot(slotId);
            });
        });
    }

    handleAddContent(slotId) {
        console.log(`Add content to slot ${slotId} in ${this.currentMode} mode`);
        this.selectedSlotId = slotId;
        this.openContentDrawer();
    }

    async handleRemoveSlot(slotId) {
        const slots = this.currentMode === 'music' ? this.musicSlots : this.mediaSlots;
        const slot = slots.find(s => s.id === slotId);
        
        if (!slot || slot.isEmpty) return;

        utils.confirm(
            'Remove Content',
            `Are you sure you want to remove content from "${slot.title}"? This will only remove it from the slot, the original file will not be deleted.`,
            async () => {
                try {
                    await backendApi.clearSlot(this.currentMode, slotId);
                    await this.loadPreviewData(); // Refresh slot data
                    Toast.success(`Content removed from ${slot.title} successfully.`, 1000);
                } catch (error) {
                    console.error('Remove slot error:', error);
                    Toast.error(`Failed to remove content from ${slot.title}.`);
                }
            }
        );
    }

    handlePlaySlot(slotId) {
        // Placeholder for future play functionality
        console.log(`Playing slot ${slotId} in ${this.currentMode} mode`);
    }

    async openContentDrawer() {
        this.drawerType = this.currentMode;
        await this.loadAvailableContentByType(this.currentMode);
        this.renderDrawerContent();
        
        const drawerTitle = document.getElementById('drawer-title');
        drawerTitle.textContent = this.drawerType === 'music' ? 'Select Background Music' : 'Select Media Content';
        
        Drawer.show('file-drawer');
        this.drawerOpen = true;
    }

    closeDrawer() {
        Drawer.hide('file-drawer');
        this.drawerOpen = false;
        this.drawerType = null;
        this.selectedSlotId = null;
    }

    renderDrawerContent() {
        const drawerBody = document.getElementById('drawer-body');
        
        if (this.drawerType === 'music') {
            this.renderMusicContent(drawerBody);
        } else {
            this.renderMediaContent(drawerBody);
        }
    }

    renderMusicContent(container) {
        if (this.availableMusicFiles.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <p>No music files available.</p>
                    <p>Please upload music files in the Material section first.</p>
                </div>
            `;
            return;
        }

        container.innerHTML = `
            <div class="drawer-file-grid audio-responsive">
                ${this.availableMusicFiles.map(file => this.createMusicCard(file)).join('')}
            </div>
        `;

        this.attachMusicCardListeners(container);
    }

    renderMediaContent(container) {
        if (this.availableMediaCompositions.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <p>No media compositions available.</p>
                    <p>Please create media compositions in the Media section first.</p>
                </div>
            `;
            return;
        }

        container.innerHTML = `
            <div class="drawer-file-grid preview-responsive">
                ${this.availableMediaCompositions.map((composition, index) => this.createMediaCard(composition, index)).join('')}
            </div>
        `;

        this.attachMediaCardListeners(container);
    }

    createMusicCard(file) {
        return `
            <div class="music-card" data-file-id="${file.file_name}">
                <div class="music-card-header">
                    <h3 class="music-card-title">${file.file_name}</h3>
                    <button class="file-card-delete-btn" data-action="delete">${Icons.RemoveIcon()}</button>
                </div>
                <div style="padding: 12px;">
                    ${utils.createCustomAudioPlayer(backendApi.getFileUrl(file.file_path))}
                </div>
            </div>
        `;
    }

    createMediaCard(composition, index) {
        // Use HTML5 video player instead of thumbnails
        const videoUrl = composition.video ? backendApi.getFileUrl(composition.video.media_path) : null;

        // Create audio player for the music part of the composition
        const audioUrl = composition.music ? 
            backendApi.getFileUrl(composition.music.media_path) : null;

        return `
            <div class="file-card" data-composition-index="${index}">
                <div class="file-card-header">
                    <h3 class="file-card-title">Media Composition ${index + 1}</h3>
                    <button class="file-card-delete-btn" data-action="delete">${Icons.RemoveIcon()}</button>
                </div>
                <div class="file-card-preview">
                    ${utils.createVideoPlayer(videoUrl, 'file-preview-video')}
                </div>
                ${audioUrl ? `
                    <div class="media-card-audio">
                        ${utils.createCustomAudioPlayer(audioUrl)}
                    </div>
                ` : `
                    <div class="file-card-meta">
                        <span class="composition-type">No Audio</span>
                    </div>
                `}
            </div>
        `;
    }

    attachMusicCardListeners(container) {
        const cards = container.querySelectorAll('.music-card');
        cards.forEach(card => {
            const fileName = card.dataset.fileId;
            const deleteBtn = card.querySelector('[data-action="delete"]');
            
            card.addEventListener('click', (e) => {
                if (e.target.closest('audio') || e.target.closest('[data-action="delete"]')) return;
                this.handleFileSelect(fileName, 'music');
            });

            deleteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.handlePermanentDelete(fileName, 'music');
            });
        });
    }

    attachMediaCardListeners(container) {
        const cards = container.querySelectorAll('.file-card');
        cards.forEach(card => {
            const compositionIndex = parseInt(card.dataset.compositionIndex);
            const deleteBtn = card.querySelector('[data-action="delete"]');
            
            card.addEventListener('click', (e) => {
                // Prevent selection when clicking on audio controls or delete button
                if (e.target.closest('[data-action="delete"]') || 
                    e.target.closest('audio') || 
                    e.target.closest('.custom-audio-player') ||
                    e.target.closest('.media-card-audio')) return;
                this.handleCompositionSelect(compositionIndex);
            });

            deleteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.handlePermanentDeleteComposition(compositionIndex);
            });
        });
    }



    async handleFileSelect(fileName, type) {
        if (this.selectedSlotId === null) return;

        try {
            if (type === 'music') {
                const musicFile = this.availableMusicFiles.find(f => f.file_name === fileName);
                await backendApi.updateSlot('music', this.selectedSlotId, '', musicFile.file_path);
            }

            await this.loadPreviewData(); // Refresh slot data
            Toast.success(`Content added to slot successfully.`, 1000);
            this.closeDrawer();
        } catch (error) {
            console.error('File select error:', error);
            Toast.error('Failed to add content to slot.');
        }
    }

    async handleCompositionSelect(compositionIndex) {
        if (this.selectedSlotId === null) return;

        const composition = this.availableMediaCompositions[compositionIndex];
        if (!composition) return;

        try {
            const videoPath = composition.video ? composition.video.media_path : '';
            const musicPath = composition.music ? composition.music.media_path : '';
            await backendApi.updateSlot('media', this.selectedSlotId, videoPath, musicPath);
            await this.loadPreviewData(); // Refresh slot data
            Toast.success(`Media composition added to slot successfully.`, 1000);
            this.closeDrawer();
        } catch (error) {
            console.error('Composition select error:', error);
            Toast.error('Failed to add composition to slot.');
        }
    }

    handlePermanentDelete(fileName, type) {
        utils.confirm(
            'Delete File Permanently',
            `Are you sure you want to permanently delete "${fileName}"? This action cannot be undone.`,
            async () => {
                try {
                    const result = await backendApi.deleteMaterial(fileName, 'audio');
                    
                    // Notify media component if it exists to clear selected files
                    if (window.mediaComponent && typeof window.mediaComponent.clearDeletedFile === 'function') {
                        window.mediaComponent.clearDeletedFile(fileName, 'audio');
                    }
                    
                    // Update affected slots immediately if any were cleared
                    if (result.cleared_slots && result.cleared_slots.length > 0) {
                        await this.updateClearedSlots(result.cleared_slots);
                        Toast.success(`${fileName} deleted permanently. ${result.cleared_slots.length} preview slot(s) were also cleared.`, 2000);
                    } else {
                        Toast.success(`${fileName} deleted permanently.`, 1000);
                    }
                    
                    // Only reload drawer content, not all preview data
                    await this.loadAvailableContentByType(this.drawerType || 'music');
                    this.renderDrawerContent();
                } catch (error) {
                    console.error('Delete error:', error);
                    Toast.error(`Failed to delete ${fileName}.`);
                }
            }
        );
    }

    handlePermanentDeleteComposition(compositionIndex) {
        const composition = this.availableMediaCompositions[compositionIndex];
        if (!composition) return;

        utils.confirm(
            'Delete Composition Permanently',
            `Are you sure you want to permanently delete "Media Composition ${compositionIndex + 1}"? This action cannot be undone.`,
            async () => {
                try {
                    const videoPath = composition.video ? composition.video.media_path : '';
                    const musicPath = composition.music ? composition.music.media_path : '';
                    const result = await backendApi.deleteMedia(videoPath, musicPath);
                    
                    // Notify media component if it exists to clear selected files that reference the deleted files
                    if (window.mediaComponent && typeof window.mediaComponent.clearDeletedFile === 'function') {
                        // Extract file names from paths to clear selected references
                        if (videoPath) {
                            const videoFileName = videoPath.split('/').pop();
                            window.mediaComponent.clearDeletedFile(videoFileName, 'video');
                        }
                        if (musicPath) {
                            const musicFileName = musicPath.split('/').pop();
                            window.mediaComponent.clearDeletedFile(musicFileName, 'audio');
                        }
                    }
                    
                    // Update affected slots immediately if any were cleared
                    if (result.cleared_slots && result.cleared_slots.length > 0) {
                        await this.updateClearedSlots(result.cleared_slots);
                        Toast.success(`Media composition deleted permanently. ${result.cleared_slots.length} preview slot(s) were also cleared.`, 2000);
                    } else {
                        Toast.success(`Media composition deleted permanently.`, 1000);
                    }
                    
                    // Only reload drawer content, not all preview data
                    await this.loadAvailableContentByType(this.drawerType || 'media');
                    this.renderDrawerContent();
                } catch (error) {
                    console.error('Delete error:', error);
                    Toast.error(`Failed to delete media composition.`);
                }
            }
        );
    }

    /**
     * Update slots that were cleared by backend operations
     * @param {string[]} clearedSlots - Array of slot descriptions like "music slot 0", "media slot 3"
     */
    async updateClearedSlots(clearedSlots) {
        let slotsUpdated = false;
        
        for (const slotDesc of clearedSlots) {
            const match = slotDesc.match(/^(music|media) slot (\d+)$/);
            if (!match) continue;
            
            const [, slotType, slotIndex] = match;
            const index = parseInt(slotIndex);
            
            if (slotType === 'music' && index < this.musicSlots.length) {
                this.musicSlots[index].isEmpty = true;
                this.musicSlots[index].content = undefined;
                slotsUpdated = true;
            } else if (slotType === 'media' && index < this.mediaSlots.length) {
                this.mediaSlots[index].isEmpty = true;
                this.mediaSlots[index].content = undefined;
                slotsUpdated = true;
            }
        }
        
        // Re-render slots if any were updated
        if (slotsUpdated) {
            this.renderSlots();
        }
    }
}

// Initialize Preview component when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('preview-tab')) {
        window.previewComponent = new PreviewComponent();
    }
}); 