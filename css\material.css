/* Material Component Styles */

/* Material Sections */
.material-section {
    margin-bottom: 32px;
}

.material-section:last-child {
    margin-bottom: 0;
}

.section-title {
    color: #262626;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 12px;
    padding-left: 4px;
}

/* File List Section */
.file-list-section {
    margin-top: 16px;
}

/* File Group Styles */
.file-group {
    margin-bottom: 12px;
    border: 1px solid #e6e6e6;
    border-radius: 8px;
    overflow: hidden;
    background-color: #ffffff;
}

.file-group:last-child {
    margin-bottom: 0;
}

.file-group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #fafafa;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.file-group-header:hover {
    background-color: #f0f0f0;
}

.file-group-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.file-group-summary {
    font-size: 12px;
    color: #666;
}

.file-group-content {
    max-height: 300px;
    overflow-y: auto;
}

/* File Item Styles */
.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.file-item:last-child {
    border-bottom: none;
}

.file-item:hover {
    background-color: #fafafa;
}

.file-item-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    min-width: 0;
}

.file-icon {
    font-size: 20px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.file-details {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    font-weight: 500;
    color: #999;
}

.file-type {
    text-transform: uppercase;
}

.file-size {
    color: inherit;
}

.separator {
    color: inherit;
}

.file-actions {
    display: flex;
    gap: 4px;
    align-items: center;
}

.file-actions .btn {
    padding: 4px 8px;
    min-width: 32px;
    height: 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.delete-button {
    color: #ff4d4f !important;
    border: none !important;
    background: transparent !important;
    cursor: pointer;
    transition: background-color 0.2s ease;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2px !important;
}

.delete-button:hover:not(:disabled) {
    background-color: #fff2f0 !important;
    border-color: transparent !important;
    color: #ff4d4f !important;
}

.remove-button {
    color: #9ca3af !important;
    border: none !important;
    background: transparent !important;
    cursor: pointer;
    transition: background-color 0.2s ease;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px !important;
    border-radius: 4px !important;
}

.remove-button svg {
    width: 16px;
    height: 16px;
}

.remove-button:hover:not(:disabled) {
    background-color: #f3f4f6 !important;
    border-color: transparent !important;
    color: #6b7280 !important;
}

/* Upload Progress */
.upload-progress {
    margin-top: 16px;
    padding: 16px;
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    border-radius: 6px;
}

.progress-text {
    font-size: 14px;
    color: #389e0d;
    margin-bottom: 8px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e6f7ff;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: #1890ff;
    transition: width 0.3s ease;
}

/* Error States */
.upload-error {
    margin-top: 16px;
    padding: 16px;
    background-color: #fff2f0;
    border: 1px solid #ffccc7;
    border-radius: 6px;
}

.error-text {
    font-size: 14px;
    color: #cf1322;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #999;
}

.empty-state p {
    margin: 8px 0;
    font-size: 14px;
}

/* Animation for file list updates */
.file-item.removing {
    animation: slideOut 0.3s ease forwards;
}

@keyframes slideOut {
    from {
        opacity: 1;
        transform: translateX(0);
        max-height: 60px;
    }
    to {
        opacity: 0;
        transform: translateX(-100%);
        max-height: 0;
        padding: 0;
        margin: 0;
    }
}

.file-item.adding {
    animation: slideInFromLeft 0.3s ease forwards;
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
} 