<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LED Fans</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/material.css">
    <link rel="stylesheet" href="css/media.css">
    <link rel="stylesheet" href="css/preview.css">
</head>
<body>
    <div class="app-container">
        <!-- Tab Navigation -->
        <nav class="tab-nav" data-active="material">
            <button class="tab-button active" data-tab="material">Material</button>
            <button class="tab-button" data-tab="media">Media</button>
            <button class="tab-button" data-tab="preview">Preview</button>
        </nav>

        <!-- Tab Content -->
        <main class="tab-content">
            <!-- Material Tab -->
            <div id="material-tab" class="tab-panel active">
                <div class="material-container">
                    <h2>Media Upload</h2>

                    <!-- Image & Video Section -->
                    <div class="material-section">
                        <h3 class="section-title">Image & Video (<span id="video-pending-count">0</span> pending)</h3>
                        <div class="upload-area" id="video-upload-area">
                            <div class="upload-icon" id="video-upload-icon"></div>
                            <p class="upload-text">Click this area to select images/videos</p>
                            <p class="upload-hint">
                                (.jpeg, .jpg, .png, .mp4, .mov)<br>
                                Maximum file size: 10MB
                            </p>
                            <input type="file" id="video-file-input" multiple accept=".jpeg,.jpg,.png,.mp4,.mov" style="display: none;">
                        </div>

                        <!-- Pending Video Files -->
                        <div class="file-list-section">
                            <div id="pending-images" class="file-group" style="display: none;">
                                <div class="file-group-header">
                                    <div class="file-group-title">Images (Pending Upload)</div>
                                    <div class="file-group-summary"></div>
                                </div>
                                <div class="file-group-content"></div>
                            </div>
                            <div id="pending-videos" class="file-group" style="display: none;">
                                <div class="file-group-header">
                                    <div class="file-group-title">Videos (Pending Upload)</div>
                                    <div class="file-group-summary"></div>
                                </div>
                                <div class="file-group-content"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Music Section -->
                    <div class="material-section">
                        <h3 class="section-title">Music (<span id="audio-pending-count">0</span> pending)</h3>
                        <div class="upload-area" id="audio-upload-area">
                            <div class="upload-icon" id="audio-upload-icon"></div>
                            <p class="upload-text">Click this area to select music files</p>
                            <p class="upload-hint">
                                (.mp3, .m4a, .wav)<br>
                                Maximum file size: 10MB
                            </p>
                            <input type="file" id="audio-file-input" multiple accept=".mp3,.m4a,.wav" style="display: none;">
                        </div>

                        <!-- Pending Audio Files -->
                        <div class="file-list-section">
                            <div id="pending-audio" class="file-group" style="display: none;">
                                <div class="file-group-header">
                                    <div class="file-group-title">Music (Pending Upload)</div>
                                    <div class="file-group-summary"></div>
                                </div>
                                <div class="file-group-content"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="action-buttons">
                        <button id="clear-pending-btn" class="btn btn-secondary" disabled>
                            Clear Pending (0)
                        </button>
                        <button id="upload-btn" class="btn btn-primary" disabled>
                            Upload (0)
                        </button>
                    </div>
                </div>
            </div>

            <!-- Media Tab -->
            <div id="media-tab" class="tab-panel">
                <div class="media-container">
                    <!-- Mode Switcher -->
                    <div class="mode-switcher" data-active="0">
                        <button class="mode-button active" data-mode="video">
                            <div class="mode-icon" id="video-mode-icon"></div>
                            <div>Video Mode</div>
                        </button>
                        <button class="mode-button" data-mode="image">
                            <div class="mode-icon" id="image-mode-icon"></div>
                            <div>Image Mode</div>
                        </button>
                    </div>

                    <!-- Video Mode -->
                    <div id="video-mode" class="mode-content active">
                        <div class="media-section">
                            <h3 class="section-title">Video Selection</h3>
                            <button class="upload-placeholder" id="select-video-btn">
                                <div class="placeholder-icon" id="video-placeholder-icon"></div>
                                <span class="placeholder-text">Click this area to select / replace Video</span>
                            </button>
                            <div id="selected-video" class="selected-file-list"></div>
                        </div>
                    </div>

                    <!-- Image Mode -->
                    <div id="image-mode" class="mode-content">
                        <div class="media-section">
                            <h3 class="section-title">Image Selection</h3>
                            <button class="upload-placeholder" id="select-images-btn">
                                <div class="placeholder-icon" id="image-placeholder-icon"></div>
                                <span class="placeholder-text">Click this area to select / replace Images</span>
                            </button>
                            <div id="selected-images" class="selected-file-list"></div>
                        </div>
                    </div>

                    <!-- Music Section -->
                    <div class="music-section">
                        <h3 class="section-title">Background Music (Optional)</h3>
                        <button class="upload-placeholder" id="select-music-btn">
                            <div class="placeholder-icon" id="music-placeholder-icon"></div>
                            <span class="placeholder-text">Click this area to select / replace Music</span>
                        </button>
                        <div id="selected-music" class="selected-file-list"></div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="action-buttons">
                        <button id="clear-media-btn" class="btn btn-secondary" disabled>Clear</button>
                        <button id="create-media-btn" class="btn btn-primary" disabled>Create Media</button>
                    </div>
                </div>
            </div>

            <!-- Preview Tab -->
            <div id="preview-tab" class="tab-panel">
                <div class="preview-container">
                    <!-- Mode Switcher -->
                    <div class="mode-switcher" data-active="0">
                        <button class="mode-button active" data-mode="music">
                            <div class="mode-icon" id="music-mode-icon"></div>
                            <div>Music Mode</div>
                        </button>
                        <button class="mode-button" data-mode="media">
                            <div class="mode-icon" id="media-mode-icon"></div>
                            <div>Media Mode</div>
                        </button>
                    </div>

                    <!-- Slots Grid -->
                    <div id="preview-slots" class="slots-grid music-mode">
                        <!-- Slots will be generated dynamically -->
                    </div>
                </div>
            </div>
        </main>

        <!-- Bottom Drawer for file selection -->
        <div id="file-drawer-backdrop" class="drawer-backdrop">
            <div id="file-drawer" class="bottom-drawer">
                <div class="drawer-header">
                    <h3 id="drawer-title" class="drawer-title">
                        <svg class="drawer-icon" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
                        </svg>
                        Select Files
                    </h3>
                    <button type="button" id="close-drawer" class="drawer-close-btn">
                        <svg class="close-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Close menu</span>
                    </button>
                </div>
                <div class="drawer-body" id="drawer-body">
                    <!-- Content will be populated dynamically -->
                </div>
            </div>
        </div>

        <!-- Preview Modal -->
        <div id="preview-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="preview-title">Preview</h3>
                    <button class="close-btn" id="close-preview">&times;</button>
                </div>
                <div class="modal-body" id="preview-body">
                    <!-- Preview content will be populated dynamically -->
                </div>
            </div>
        </div>

        <!-- Toast notifications -->
        <div id="toast-container" class="toast-container"></div>
    </div>

    <!-- Scripts -->
    <script type="module" src="https://cdn.jsdelivr.net/npm/media-chrome@3/+esm"></script>
    <script src="js/icons.js"></script>
    <script src="js/api.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/material.js"></script>
    <script src="js/media.js"></script>
    <script src="js/preview.js"></script>
    <script src="js/app.js"></script>
</body>
</html> 